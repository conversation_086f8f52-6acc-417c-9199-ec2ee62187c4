name: 📝 Detailed Feature Proposal
description: Propose a new feature or improvement
labels: ["proposal"]
body:
    - type: markdown
      attributes:
          value: |
              **Feature Proposal for Cline**

              Thank you for creating a feature proposal for Cline! This template is for clear, actionable proposals that define a specific problem and a high-confidence solution. Please provide enough detail to enable fast prioritization, discussion, and execution.

              Detailed proposals will be prioritized, while vague proposals may be closed or require extensive back and forth communication.

              Before submitting:
              - Search existing [Issues](https://github.com/cline/cline/issues) and [Discussions](https://github.com/cline/cline/discussions) to avoid duplicates
              - Don’t start implementation until the proposal is reviewed and approved

    - type: textarea
      id: problem-description
      attributes:
          label: What problem does this solve?
          description: |
              Describe the problem clearly from a user's point of view. Focus on why this matters, who it affects, and when it occurs.

              ✅ Solid:
              - "LLM provider returns 400 error when nearing the context window instead of truncating"
              - "Submit button is invisible in dark mode"

              ❌ Avoid:
              - "Performance is bad"
              - "UI needs work"

              Your description should include:
              - Who is affected?
              - When does it happen?
              - What's the current vs expected behavior?
              - What is the impact?
          placeholder: Be specific about the problem, who it affects, and the impact.
      validations:
          required: true

    - type: textarea
      id: proposed-solution
      attributes:
          label: What’s the proposed solution?
          description: |
              Describe how the problem should be solved. Be specific about UX, system behavior, and any flows that would change.

              ✅ Solid:
              - "Add error handling immediately after attempting to create the llm stream and retry after manually truncating"
              - "Update button styling to ensure contrast in all themes"

              ❌ Avoid:
              - "Improve performance"
              - "Fix the bug"

              Your solution should include:
              - What exactly will change?
              - How will users interact with it?
              - What’s the expected outcome?
          placeholder: Describe the proposed changes and how they solve the problem.
      validations:
          required: true

    - type: textarea
      id: acceptance-criteria
      attributes:
          label: How will we know it works? (Acceptance Criteria)
          description: Define clear, testable success criteria.
          placeholder: Provide specific and testable conditions for success.
      validations:
          required: true

    - type: textarea
      id: estimated-effort
      attributes:
          label: Estimated effort and complexity
          description: |
              Help us understand scope and risks. Include:

              - Size estimate (XS/S/M/L/XL or hours/days)
              - Why this size? What’s technically involved?
              - Any tricky parts, refactors, or risks?
              - Performance or compatibility concerns?
              - Any dependencies on systems, teams, or libraries?
          placeholder: Size, reasoning, risks, and dependencies.
      validations:
          required: true

    - type: textarea
      id: technical-considerations
      attributes:
          label: Technical considerations, tradeoffs, and/or risks (optional)
          description: |
              Include any technical context that helps us evaluate or implement the proposal more effectively.

              You may include:
              - Architectural changes or required refactors
              - Performance implications or system-level impacts
              - Known limitations, risks, or tricky edge cases
              - Compatibility concerns or migration steps
              - Alternative approaches you considered and why they were not chosen
              - Dependencies on other systems, teams, or libraries
              - Were other approaches considered? Why is this one preferred?
          placeholder: Technical considerations, tradeoffs, and/or risks.

    - type: textarea
      id: additional-context
      attributes:
          label: Additional context (optional)
          description: Diagrams, mockups, logs, links, or anything else that helps explain or justify the proposal.
          placeholder: Diagrams, mockups, logs, links, or anything else that helps explain or justify the proposal.

    - type: checkboxes
      id: checklist
      attributes:
          label: Proposal checklist
          options:
              - label: I’ve checked for existing issues or related proposals
                required: true
              - label: I understand this needs review before implementation can start
                required: true

    - type: checkboxes
      id: willingness-to-contribute
      attributes:
          label: Interested in implementing this?
          description: Optional
          options:
              - label: Yes, I’d like to help implement this feature
