# Cline Autocomplete 功能

Cline 现在支持智能代码自动补全功能，可以在您编写代码时提供上下文感知的建议。

## 功能特性

- **智能代码补全**: 基于当前文件上下文和项目结构提供代码建议
- **多语言支持**: 支持 JavaScript、TypeScript、Python、Java、C++ 等多种编程语言
- **上下文感知**: 分析当前光标位置的代码上下文，提供相关的补全建议
- **实时预览**: 在编辑器中实时显示补全建议
- **成本跟踪**: 显示 API 调用成本和使用统计

## 启用 Autocomplete

### 1. 启用实验标志

首先需要启用 autocomplete 实验标志：

1. 打开 VS Code 命令面板 (`Cmd+Shift+P` 或 `Ctrl+Shift+P`)
2. 搜索并执行 `Cline: Enable Autocomplete` 命令
3. 您会看到确认消息："Autocomplete experiment enabled! The feature will be available shortly."

### 2. 配置 API 设置

Autocomplete 功能需要 OpenRouter API 密钥：

1. 在 Cline 设置中配置您的 OpenRouter API 密钥
2. 确保您有足够的 API 配额来使用自动补全功能

### 3. 配置选项

您可以在 VS Code 设置中配置以下选项：

- `cline.autocomplete.enabled`: 启用/禁用自动补全功能 (默认: true)
- `cline.autocomplete.model`: 用于补全的模型 (默认: "google/gemini-2.5-flash-preview-05-20")
- `cline.autocomplete.maxCompletions`: 每个上下文的最大补全数量 (默认: 5)
- `cline.autocomplete.debounceMs`: UI 更新的防抖时间 (默认: 250ms)
- `cline.autocomplete.maxLines`: 处理的最大行数 (默认: 100)

## 使用方法

### 基本使用

1. 在支持的文件类型中开始编写代码
2. 当您停止输入时，autocomplete 会自动分析上下文
3. 如果有合适的建议，会在编辑器中显示灰色的预览文本
4. 按 `Tab` 键接受建议，或继续输入忽略建议

### 快捷键

- `Tab`: 接受当前的自动补全建议
- `Escape`: 取消当前的补全预览
- `Cmd+K` (Mac) / `Ctrl+K` (Windows/Linux): 手动触发补全

### 状态栏

状态栏会显示 autocomplete 的状态：

- `$(sparkle) Kilo Complete ($0.00)`: 功能已启用，显示当前会话成本
- `$(warning) Kilo Complete`: 需要配置 API 密钥
- `$(circle-slash) Kilo Complete`: 功能已禁用

## 支持的文件类型

Autocomplete 支持以下文件类型：

- JavaScript (`.js`, `.jsx`)
- TypeScript (`.ts`, `.tsx`)
- Python (`.py`)
- Java (`.java`)
- C/C++ (`.c`, `.cpp`, `.h`, `.hpp`)
- Go (`.go`)
- Rust (`.rs`)
- PHP (`.php`)
- Ruby (`.rb`)
- 以及更多...

## 故障排除

### Autocomplete 不工作

1. 确认实验标志已启用：运行 `Cline: Enable Autocomplete` 命令
2. 检查 OpenRouter API 密钥是否正确配置
3. 确认 `cline.autocomplete.enabled` 设置为 `true`
4. 检查状态栏是否显示警告信息

### 补全建议质量不佳

1. 尝试更换不同的模型（在设置中修改 `cline.autocomplete.model`）
2. 确保当前文件有足够的上下文信息
3. 检查项目结构是否被正确分析

### 性能问题

1. 减少 `cline.autocomplete.maxLines` 设置
2. 增加 `cline.autocomplete.debounceMs` 以减少 API 调用频率
3. 在大型文件中考虑禁用 autocomplete

## 成本管理

Autocomplete 功能会产生 API 调用成本：

- 每次补全请求都会消耗 API 配额
- 状态栏显示当前会话的总成本
- 建议合理设置防抖时间以控制成本

## 隐私和安全

- 代码内容会发送到配置的 AI 模型进行处理
- 请确保您的项目符合相关的隐私和安全要求
- 敏感项目建议使用本地模型或禁用此功能

## 反馈和支持

如果您遇到问题或有改进建议，请：

1. 检查 VS Code 开发者控制台的错误信息
2. 在 Cline GitHub 仓库提交 issue
3. 提供详细的错误信息和重现步骤
