import { expect } from "chai"
import { describe, it, beforeEach, afterEach } from "mocha"
import { createTestEnvironment, createTestTracker } from "./Checkpoint-test-utils"
import CheckpointTracker from "./CheckpointTracker"

describe("Checkpoint Creation", () => {
    let env: Awaited<ReturnType<typeof createTestEnvironment>>

    beforeEach(async () => {
        env = await createTestEnvironment()
    })

    afterEach(async () => {
        await env.cleanup()
    })

    it("should create a new checkpoint tracker", async () => {
        const tracker = await createTestTracker(env.globalStoragePath)
        expect(tracker).to.not.be.undefined
        expect(tracker).to.be.instanceOf(CheckpointTracker)

        // Verify shadow git config
        const configWorkTree = await tracker?.getShadowGitConfigWorkTree()
        expect(configWorkTree).to.not.be.undefined
    })

    it("should throw error when globalStoragePath is missing", async () => {
        try {
            await createTestTracker(undefined)
            expect.fail("Expected error was not thrown")
        } catch (error: any) {
            expect(error.message).to.equal("Global storage path is required to create a checkpoint tracker")
        }
    })
})
