import * as vscode from "vscode"
import { getGlobalState, getSecret } from "../../core/storage/state"
import { ApiConfiguration } from "../../shared/api"

/**
 * ContextProxy provides access to extension state and configuration
 * This is a simplified implementation to replace the original ContextProxy dependency
 */
export class ContextProxy {
	private static _instance: ContextProxy | null = null
	private context: vscode.ExtensionContext | null = null

	private constructor() {}

	public static get instance(): ContextProxy | null {
		return ContextProxy._instance
	}

	public static initialize(context: vscode.ExtensionContext): void {
		if (!ContextProxy._instance) {
			ContextProxy._instance = new ContextProxy()
		}
		ContextProxy._instance.context = context
	}

	public getGlobalState(key: string): any {
		if (!this.context) {
			console.warn("ContextProxy: Extension context not initialized")
			return undefined
		}

		// Handle experiments specifically
		if (key === "experiments") {
			// Read from global state, default to autocomplete enabled
			const experiments = this.context.globalState.get("experiments") as Record<string, boolean> | undefined
			return experiments || { autocomplete: true }
		}

		return this.context.globalState.get(key)
	}

	public getProviderSettings(): {
		kilocodeToken?: string
		[key: string]: any
	} {
		if (!this.context) {
			console.warn("ContextProxy: Extension context not initialized")
			return {}
		}

		// Try to get kilocode token from secrets storage
		// This integrates with Cline's existing secret storage system
		try {
			const kilocodeToken = this.context.secrets.get("kilocodeApiKey")
			return {
				kilocodeToken: kilocodeToken || undefined
			}
		} catch (error) {
			console.warn("ContextProxy: Error reading kilocode token from secrets", error)
			return {
				kilocodeToken: undefined
			}
		}
	}

	public async setExperimentFlag(experimentId: string, enabled: boolean): Promise<void> {
		if (!this.context) {
			console.warn("ContextProxy: Extension context not initialized")
			return
		}

		const experiments = this.getGlobalState("experiments") || {}
		experiments[experimentId] = enabled
		await this.context.globalState.update("experiments", experiments)
	}

	public dispose(): void {
		this.context = null
		ContextProxy._instance = null
	}
}
