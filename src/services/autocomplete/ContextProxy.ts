import * as vscode from "vscode"
import { getGlobalState, getSecret } from "../../core/storage/state"
import { ApiConfiguration } from "../../shared/api"

/**
 * ContextProxy provides access to extension state and configuration
 * This is a simplified implementation to replace the original ContextProxy dependency
 */
export class ContextProxy {
	private static _instance: ContextProxy | null = null
	private context: vscode.ExtensionContext | null = null

	private constructor() {}

	public static get instance(): ContextProxy | null {
		return ContextProxy._instance
	}

	public static initialize(context: vscode.ExtensionContext): void {
		if (!ContextProxy._instance) {
			ContextProxy._instance = new ContextProxy()
		}
		ContextProxy._instance.context = context
	}

	public getGlobalState(key: string): any {
		if (!this.context) {
			console.warn("ContextProxy: Extension context not initialized")
			return undefined
		}

		// Handle experiments specifically
		if (key === "experiments") {
			// Read from global state, default to autocomplete enabled
			const experiments = this.context.globalState.get("experiments") as Record<string, boolean> | undefined
			return experiments || { autocomplete: true }
		}

		return this.context.globalState.get(key)
	}

	public getProviderSettings(): {
		kilocodeToken?: string
		[key: string]: any
	} {
		if (!this.context) {
			console.warn("ContextProxy: Extension context not initialized")
			return {}
		}

		// For now, return empty settings since we don't have a synchronous way to read secrets
		// In a real implementation, this would need to be async or use cached values
		// The autocomplete functionality will need to be updated to handle this properly
		return {
			kilocodeToken: undefined, // This needs to be connected to Cline's API settings
		}
	}

	public getAutocompleteConfig(): {
		enabled: boolean
		model: string
		maxCompletions: number
		debounceMs: number
		maxLines: number
	} {
		if (!this.context) {
			console.warn("ContextProxy: Extension context not initialized")
			return {
				enabled: true,
				model: "google/gemini-2.5-flash-preview-05-20",
				maxCompletions: 5,
				debounceMs: 250,
				maxLines: 100,
			}
		}

		// Read configuration from VS Code settings
		const config = vscode.workspace.getConfiguration("cline.autocomplete")

		return {
			enabled: config.get<boolean>("enabled", true),
			model: config.get<string>("model", "google/gemini-2.5-flash-preview-05-20"),
			maxCompletions: config.get<number>("maxCompletions", 5),
			debounceMs: config.get<number>("debounceMs", 250),
			maxLines: config.get<number>("maxLines", 100),
		}
	}

	public async setExperimentFlag(experimentId: string, enabled: boolean): Promise<void> {
		if (!this.context) {
			console.warn("ContextProxy: Extension context not initialized")
			return
		}

		const experiments = this.getGlobalState("experiments") || {}
		experiments[experimentId] = enabled
		await this.context.globalState.update("experiments", experiments)
	}

	public dispose(): void {
		this.context = null
		ContextProxy._instance = null
	}
}
