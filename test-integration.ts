// Integration test for autocomplete functionality
import * as vscode from "vscode"
import { ContextProxy } from "./src/services/autocomplete/ContextProxy"

// Mock VS Code extension context for testing
const mockContext: vscode.ExtensionContext = {
	subscriptions: [],
	workspaceState: {
		get: (key: string) => undefined,
		update: async (key: string, value: any) => {},
		keys: () => [],
	},
	globalState: {
		get: (key: string) => {
			if (key === "experiments") {
				return { autocomplete: true }
			}
			return undefined
		},
		update: async (key: string, value: any) => {},
		keys: () => [],
		setKeysForSync: (keys: string[]) => {},
	},
	secrets: {
		get: async (key: string) => undefined,
		store: async (key: string, value: string) => {},
		delete: async (key: string) => {},
		onDidChange: new vscode.EventEmitter<vscode.SecretStorageChangeEvent>().event,
	},
	extensionUri: vscode.Uri.file("/test"),
	extensionPath: "/test",
	environmentVariableCollection: {} as any,
	asAbsolutePath: (relativePath: string) => "/test/" + relativePath,
	storageUri: vscode.Uri.file("/test/storage"),
	globalStorageUri: vscode.Uri.file("/test/global"),
	logUri: vscode.Uri.file("/test/log"),
	extensionMode: vscode.ExtensionMode.Development,
	extension: {} as any,
	languageModelAccessInformation: {} as any,
}

// Test ContextProxy initialization
function testContextProxy() {
	console.log("Testing ContextProxy initialization...")

	// Initialize ContextProxy
	ContextProxy.initialize(mockContext)

	// Test instance access
	const instance = ContextProxy.instance
	if (!instance) {
		throw new Error("ContextProxy instance should not be null after initialization")
	}

	// Test getGlobalState
	const experiments = instance.getGlobalState("experiments")
	console.log("Experiments:", experiments)

	if (!experiments || !experiments.autocomplete) {
		throw new Error("Autocomplete experiment should be enabled")
	}

	// Test getAutocompleteConfig
	const config = instance.getAutocompleteConfig()
	console.log("Autocomplete config:", config)

	if (!config.enabled) {
		console.warn("Autocomplete is disabled in config")
	}

	console.log("ContextProxy tests passed!")
}

// Test autocomplete configuration
function testAutocompleteConfig() {
	console.log("Testing autocomplete configuration...")

	const instance = ContextProxy.instance
	if (!instance) {
		throw new Error("ContextProxy not initialized")
	}

	const config = instance.getAutocompleteConfig()

	// Verify default values
	if (config.model !== "google/gemini-2.5-flash-preview-05-20") {
		throw new Error(`Expected default model to be 'google/gemini-2.5-flash-preview-05-20', got '${config.model}'`)
	}

	if (config.maxCompletions !== 5) {
		throw new Error(`Expected maxCompletions to be 5, got ${config.maxCompletions}`)
	}

	if (config.debounceMs !== 250) {
		throw new Error(`Expected debounceMs to be 250, got ${config.debounceMs}`)
	}

	if (config.maxLines !== 100) {
		throw new Error(`Expected maxLines to be 100, got ${config.maxLines}`)
	}

	console.log("Autocomplete configuration tests passed!")
}

// Run all tests
function runTests() {
	try {
		testContextProxy()
		testAutocompleteConfig()
		console.log("All integration tests passed!")
	} catch (error) {
		console.error("Integration test failed:", error)
		process.exit(1)
	}
}

// Export for potential use
export { runTests }

// Run tests if this file is executed directly
if (require.main === module) {
	runTests()
}
